from dataclasses import dataclass
from pprint import pprint
from scipy.spatial.transform import Rotation as R
import numpy as np
import open3d as o3d
from loguru import logger
import cv2 as cv

from camera_realsense import RealSenseCamera

# Global visualizer instance for reuse
_visualizer = None
_pcd_geometry = None
_obb_geometry = None
_outlier_geometry = None
_should_stop = False


@dataclass
class BoxPosition:
    center: np.ndarray
    z_position: float
    extent: np.ndarray
    rotation: np.ndarray  # zxy
    volume: float


# Register key callback for escape key detection
def escape_key_callback(vis):
    global _should_stop
    logger.info("Escape key pressed, stopping program")
    _should_stop = True
    return False


def get_box_position(pcd, visualize=False):
    # ROI box (without conveyor)
    pcd = pcd.crop(
        o3d.geometry.AxisAlignedBoundingBox(
            min_bound=(-0.3, -0.55, 0.15), max_bound=(0.3, 0.45, 3)
        )
    )

    points = np.asarray(pcd.points)

    # Get top (low Z value) of box by taking 10th percentile of Z values
    z_values = points[:, 2]
    try:
        top_box_z = float(np.percentile(z_values, 10))
    except IndexError:
        logger.error("Failed to compute top of box (10th percentile Z)")
        return None
    logger.trace(f"Top of box (10th percentile Z): {top_box_z:.3f} mm")

    # Isolate top of box
    pcd = pcd.crop(
        o3d.geometry.AxisAlignedBoundingBox(
            min_bound=(-np.inf, -np.inf, top_box_z - 0.1),
            max_bound=(np.inf, np.inf, top_box_z + 0.1),
        )
    )
    points = np.asarray(pcd.points)
    logger.trace(
        f"Min and max Z after cropping: {points[:, 2].min():.3f} to {points[:, 2].max():.3f} mm"
    )

    # Remove single points (noise)
    cl, ind = pcd.remove_statistical_outlier(nb_neighbors=20, std_ratio=0.5)
    # cl, ind = pcd.remove_radius_outlier(nb_points=30, radius=0.01)
    outlier_cloud = pcd.select_by_index(ind, invert=True)
    outlier_cloud.paint_uniform_color([1, 0, 0])
    pcd = pcd.select_by_index(ind)

    # # Select the edges (less work for OBB in the next step, which makes it more stable)
    # cl2, ind2 = pcd.remove_radius_outlier(nb_points=30, radius=0.01)
    # pcd = pcd.select_by_index(ind2, invert=True)

    voxel_grid = o3d.geometry.VoxelGrid.create_from_point_cloud(pcd, voxel_size=0.01)

    pixels_2d = np.zeros((60, 100), dtype=np.uint8)
    for voxel in voxel_grid.get_voxels():
        index = voxel.grid_index
        x = index[0]
        y = index[1]
        pixels_2d[x, y] = 255

    cv.imshow("Voxel Grid", pixels_2d)
    cv.waitKey(1)

    print(f"Number of points after filtering: {len(pcd.points)}")

    try:
        obb = voxel_grid.get_minimal_oriented_bounding_box()
    except RuntimeError as e:
        logger.error(f"Failed to compute OBB: {e}")
        return None

    logger.trace(f"OBB center: {obb.center}")
    logger.trace(f"OBB extent: {obb.extent}")
    logger.trace(f"OBB rotation matrix: {obb.R}")
    logger.trace(f"OBB volume: {obb.volume()}")

    # We want to have the longest axis of the OBB be the length: the first axis.
    # The second axis should be the width, and the third axis should be the height.
    # The shortest axis of the OBB is the height.
    obb_extent = obb.extent.copy()
    obb_R = obb.R.copy()
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    if obb_extent[1] < obb_extent[2]:
        obb_extent = np.array([obb_extent[0], obb_extent[2], obb_extent[1]])
        obb_R = obb_R @ np.array([[1, 0, 0], [0, 0, 1], [0, -1, 0]])
    if obb_extent[0] < obb_extent[1]:
        obb_extent = np.array([obb_extent[1], obb_extent[0], obb_extent[2]])
        obb_R = obb_R @ np.array([[0, 1, 0], [-1, 0, 0], [0, 0, 1]])
    logger.trace(f"OBB extent after sorting: {obb_extent}")

    # Get rotation angles from rotation matrix
    r = R.from_matrix(obb_R)
    angles = r.as_euler("zxy", degrees=True)
    logger.trace(
        f"z rotation: {angles[0]:.3f} deg, x rotation: {angles[1]:.3f} deg, y rotation: {angles[2]:.3f} deg"
    )

    if visualize:
        global _visualizer, _pcd_geometry, _obb_geometry, _outlier_geometry

        # Initialize visualizer if not already created
        if _visualizer is None:
            _visualizer = o3d.visualization.VisualizerWithKeyCallback()  # type: ignore
            _visualizer.create_window(window_name="PLY Viewer")

            _visualizer.register_key_callback(256, escape_key_callback)  # ESC key

            # Set rendering options for better visualization
            render_option = _visualizer.get_render_option()
            render_option.point_size = 2.0  # Make points more visible
            render_option.background_color = np.array([0.1, 0.1, 0.1])  # Dark background

            # Add initial geometries and store references
            _pcd_geometry = pcd
            _obb_geometry = obb
            _outlier_geometry = outlier_cloud
            _visualizer.add_geometry(_pcd_geometry)
            _visualizer.add_geometry(_obb_geometry)
            _visualizer.add_geometry(_outlier_geometry)
            _visualizer.add_geometry(voxel_grid)

            # # Set initial viewpoint after adding geometries
            # _visualizer.get_view_control().set_front([0.0, -1.0, -1.0])
            # _visualizer.get_view_control().set_lookat([0.0, 0.0, 800.0])
            # _visualizer.get_view_control().set_up([0.0, 0.0, 1.0])
        else:
            assert _pcd_geometry is not None
            assert _obb_geometry is not None
            assert _outlier_geometry is not None
            # Update existing geometries in place
            _pcd_geometry.points = pcd.points
            _pcd_geometry.colors = pcd.colors
            _outlier_geometry.points = outlier_cloud.points
            _outlier_geometry.colors = outlier_cloud.colors
            if hasattr(pcd, "normals") and len(pcd.normals) > 0:
                _pcd_geometry.normals = pcd.normals

            # Update OBB geometry
            _obb_geometry.center = obb.center
            _obb_geometry.extent = obb.extent
            _obb_geometry.R = obb.R
            _obb_geometry.color = obb.color

            # Update the visualizer with the modified geometries
            _visualizer.update_geometry(_pcd_geometry)
            _visualizer.update_geometry(_obb_geometry)
            _visualizer.update_geometry(_outlier_geometry)

        # Non-blocking update - this allows interaction
        _visualizer.poll_events()
        _visualizer.update_renderer()

    return BoxPosition(obb.center, top_box_z, obb_extent, angles, obb.volume())


def cleanup_visualizer():
    """Clean up the global visualizer instance."""
    global _visualizer, _pcd_geometry, _obb_geometry, _outlier_geometry
    if _visualizer is not None:
        _visualizer.destroy_window()
        _visualizer = None
        _pcd_geometry = None
        _obb_geometry = None
        _outlier_geometry = None


if __name__ == "__main__":
    camera = RealSenseCamera()
    if not camera.start_capture():
        raise RuntimeError("Failed to start camera capture")

    try:
        while not _should_stop:
            pcd = camera.get_pointcloud()
            assert pcd is not None

            # # ROI box (including conveyor)
            # pcd = pcd.crop(o3d.geometry.AxisAlignedBoundingBox(min_bound=(-330, -680, 400), max_bound=(250, 500, 2000)))

            box_position = get_box_position(pcd, visualize=True)
            pprint(box_position)
    finally:
        camera.stop_capture()
        cleanup_visualizer()
